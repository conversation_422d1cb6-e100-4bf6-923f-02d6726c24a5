<?php

namespace App\Providers;

use Illuminate\Support\Collection;
use <PERSON><PERSON>\Telescope\IncomingEntry;
use <PERSON><PERSON>\Telescope\Telescope;
use <PERSON><PERSON>\Telescope\TelescopeApplicationServiceProvider;

class TelescopeServiceProvider extends TelescopeApplicationServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        Telescope::night();

        $this->hideSensitiveRequestDetails();

        $isLocal = $this->app->environment('local');
        $isStaging = $this->app->environment('staging');
        $isProduction = $this->app->environment('production');

        Telescope::filterBatch(function (Collection $entries) use ($isLocal, $isStaging, $isProduction) {
            // In local environment, show everything
            if ($isLocal) {
                return true;
            }

            // In staging and production, only show errors and exceptions
            if ($isStaging || $isProduction) {
                return $entries->contains(function (IncomingEntry $entry) {
                    return $entry->isReportableException() ||
                        $entry->isFailedJob() ||
                        ($entry->type === 'log' && in_array($entry->content['level'], ['error', 'critical', 'alert', 'emergency']));
                });
            }

            return false;
        });
    }

    /**
     * Prevent sensitive request details from being logged by Telescope.
     */
    protected function hideSensitiveRequestDetails(): void
    {
        if ($this->app->environment('local')) {
            return;
        }

        Telescope::hideRequestParameters(['_token']);

        Telescope::hideRequestHeaders([
            'cookie',
            'x-csrf-token',
            'x-xsrf-token',
        ]);
    }

    protected function authorization(): void
    {
        Telescope::auth(function ($request) {
            // In local environment, allow access without authentication
            if ($this->app->environment('local')) {
                return true;
            }

            // In staging and production, require authentication from any guard
            return $request->user('admin') ||
                   $request->user('staff') ||
                   $request->user('customer') ||
                   $request->user('web');
        });
    }
}
