<?php

namespace App\Http\Controllers\Customer;

use App\Models\Warehouse;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\CustomerProductExport;

class ProductController extends Controller
{
    public function index(Warehouse $warehouse)
    {
        return view('customer.product.index', compact('warehouse'));
    }

    public function query(Warehouse $warehouse)
    {
        $customer = $warehouse->warehouseCustomers()->where('customer_id', Auth::id())->first();

        $query = $warehouse->customerProducts()
            ->where('warehouse_customer_id', $customer->id)
            ->select('warehouse_customer_products.*')
            ->orderByRaw('sequence IS NULL, sequence ASC');

        $result = DataTables::of($query)
            ->addColumn('image', function ($row) {
                return '<div class="symbol symbol-50px"><img src="' . $row->image . '" alt="SKU Image" class="w-100"></div>';
            })
            ->editColumn('is_active', function ($row) {
                return $row->is_active ? '<span class="badge badge-success">Active</span>' : '<span class="badge badge-danger">Inactive</span>';
            })
            ->addColumn('customer', function ($row) {
                return $row->warehouseCustomer->name;
            })
            ->addColumn('uomType.name', function ($row) {
                return $row->uomType->name ?? '-';
            })
            ->addColumn('total_stock', function ($row) {
                return $row->productStocks->sum('quantity') ?? '-';
            })
            ->addColumn('total_cubage', function ($row) {
                $totalStock = $row->productStocks->sum('quantity');
                return $totalStock > 0 && $row->cubage ? number_format($totalStock * $row->cubage, 3) . ' m³' : '-';
            })
            ->addColumn('total_weight', function ($row) {
                $totalStock = $row->productStocks->sum('quantity');
                return $totalStock > 0 && $row->weight ? number_format($totalStock * $row->weight, 2) . ' kg' : '-';
            })
            ->editColumn('created_at', function ($row) {
                return $row->created_at;
            })
            ->addColumn('actions', function () {})
            ->rawColumns(['image', 'is_active', 'actions'])->make(true);

        return $result;
    }

    public function export(Warehouse $warehouse)
    {
        $customer = $warehouse->warehouseCustomers()->where('customer_id', Auth::id())->first();

        $query = $warehouse->customerProducts()
            ->where('warehouse_customer_id', $customer->id)
            ->orderByRaw('sequence IS NULL, sequence ASC')
            ->get();

        return Excel::download(new CustomerProductExport($query), 'my_products_' . $warehouse->name . '_' . now()->format('Y-m-d_H-i-s') . '.xlsx');
    }
}
