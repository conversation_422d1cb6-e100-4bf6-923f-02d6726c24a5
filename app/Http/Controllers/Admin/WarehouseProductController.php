<?php

namespace App\Http\Controllers\Admin;

use App\Models\CustomerProductCategoryMapping;
use App\Models\Warehouse;
use App\Models\WarehouseCustomer;
use App\Models\WarehouseCustomerProduct;
use App\Models\WarehouseUomType;
use App\Services\Photoshop;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Excel;
use Maatwebsite\Excel\Facades\Excel as ExcelFacade;
use App\Exports\AdminProductExport;
use Validator;
use Yajra\DataTables\DataTables;

class WarehouseProductController extends Controller
{
    public function index(Warehouse $warehouse)
    {
        return view('admin.warehouse.product.index', compact('warehouse'));
    }

    public function query(Warehouse $warehouse)
    {
        $query = $warehouse->customerProducts()
            ->with(['uomType', 'warehouseCustomer', 'productStocks'])
            ->orderByRaw('sequence IS NULL, sequence ASC')
            ->select('warehouse_customer_products.*');

        $result = DataTables::of($query)
            ->addColumn('image', function ($row) {
                return '<div class="symbol symbol-50px"><img src="' . ($row->image ?: asset('dashboard-assets/media/avatars/sku-blank.png')) . '" alt="Product Image" class="w-100"></div>';
            })
            ->addColumn('customer', function ($row) {
                return $row->warehouseCustomer->name ?? '-';
            })
            ->addColumn('total_cubage', function ($row) {
                $totalStock = $row->productStocks->sum('quantity');
                return $totalStock > 0 && $row->cubage ? number_format($totalStock * $row->cubage, 3) . ' m³' : '-';
            })
            ->addColumn('total_weight', function ($row) {
                $totalStock = $row->productStocks->sum('quantity');
                return $totalStock > 0 && $row->weight ? number_format($totalStock * $row->weight, 2) . ' kg' : '-';
            })
            ->addColumn('total_stock', function ($row) {
                return $row->productStocks->sum('quantity');
            })
            ->editColumn('is_active', function ($row) {
                return $row->is_active ? '<span class="badge badge-success">Active</span>' : '<span class="badge badge-danger">Inactive</span>';
            })
            ->addColumn('actions', function ($row) use ($warehouse) {
                $actions = '<div class="d-flex flex-column justify-content-start">';
                $actions .= ' <a href="' . route('admin.warehouse.product.edit', [$warehouse, $row->id]) . '" class="btn btn-sm btn-success mb-2">Edit</a>';
                $actions .= '</div>';

                return $actions;
            })
            ->addColumn('uomType.name', function ($row) {
                return $row->uomType->name ?? '-';
            })
            ->editColumn('created_at', function ($row) {
                return $row->created_at->format('Y-m-d H:i:s');
            })
            ->rawColumns(['image', 'is_active', 'actions'])
            ->make(true);

        return $result;
    }

    public function create(Warehouse $warehouse)
    {
        return view('admin.warehouse.product.create', compact('warehouse'));
    }

    public function store(Photoshop $photoshop, Request $request, Warehouse $warehouse)
    {
        Validator::make($request->all(), [
            'name' => 'required',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:15360',
            'sku' => 'required|unique:warehouse_customer_products,sku',
            'weight' => 'nullable|numeric|min:0',
            'cubage' => 'nullable|numeric|min:0',
            'warehouse_customer_id' => 'required|exists:warehouse_customer,id',
            'warehouse_uom_type_id' => 'required|exists:warehouse_uom_types,id',
            'warehouse_product_category_id' => 'required|exists:warehouse_product_categories,id',
        ])->validate();

        $customer_product = new WarehouseCustomerProduct;
        $customer_product->fill($request->all());
        $customer_product->warehouse_customer_id = $request->get('warehouse_customer_id');
        $customer_product->warehouse_uom_type_id = $request->get('warehouse_uom_type_id');

        if ($request->hasFile('image')) {
            $filename = $request->file('image')->hashName('products');
            $resized = $photoshop->take($request->file('image'))->scale(width: 300)->encode();
            Storage::disk('public')->put($filename, $resized);
            $customer_product->image = asset('storage/' . $filename);
        }

        $customer_product->save();

        $categoryMapping = new CustomerProductCategoryMapping;
        $categoryMapping->warehouse_customer_product_id = $customer_product->id;
        $categoryMapping->warehouse_product_category_id = $request->get('warehouse_product_category_id');
        $categoryMapping->save();

        Session::flash('alert-success', 'Successfully Created');

        return redirect()->route('admin.warehouse.product', $warehouse);
    }

    public function edit(Warehouse $warehouse, WarehouseCustomerProduct $customer_product)
    {
        return view('admin.warehouse.product.edit', compact('warehouse', 'customer_product'));
    }

    public function update(Photoshop $photoshop, Request $request, Warehouse $warehouse, WarehouseCustomerProduct $customer_product)
    {
        Validator::make($request->all(), [
            'name' => 'required',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:15360',
            'sku' => 'required|unique:warehouse_customer_products,sku,' . $customer_product->id,
            'weight' => 'nullable|numeric|min:0',
            'cubage' => 'nullable|numeric|min:0',
            'warehouse_customer_id' => 'required|exists:warehouse_customer,id',
            'warehouse_uom_type_id' => 'required|exists:warehouse_uom_types,id',
            'warehouse_product_category_id' => 'required|exists:warehouse_product_categories,id',
        ])->validate();

        $customer_product->fill($request->all());
        $customer_product->warehouse_customer_id = $request->get('warehouse_customer_id');
        $customer_product->warehouse_uom_type_id = $request->get('warehouse_uom_type_id');

        if ($request->hasFile('image')) {
            $filename = $request->file('image')->hashName('products');
            $resized = $photoshop->take($request->file('image'))->scale(width: 300)->encode();
            Storage::disk('public')->put($filename, $resized);
            $customer_product->image = asset('storage/' . $filename);
        }

        $customer_product->save();

        $categoryMapping = new CustomerProductCategoryMapping;
        $categoryMapping->warehouse_customer_product_id = $customer_product->id;
        $categoryMapping->warehouse_product_category_id = $request->get('warehouse_product_category_id');
        $categoryMapping->save();

        Session::flash('alert-success', 'Successfully Updated');

        return redirect()->route('admin.warehouse.product', $warehouse);
    }

    public function delete(Warehouse $warehouse, WarehouseCustomerProduct $customer_product)
    {
        if ($customer_product->orderItems()->count() > 0) {
            Session::flash('alert-danger', 'Failed to delete. This product has been associated with orders.');

            return redirect()->back();
        }

        $customer_product->delete();
        Session::flash('alert-success', 'Successfully Deleted');

        return redirect()->route('admin.warehouse.product', $warehouse);
    }

    public function readNImportExcelRow(Request $request, Warehouse $warehouse, Excel $excel)
    {
        $validator = Validator::make($request->all(), [
            'excel_file' => 'required|mimes:xlsx,xls|max:10240',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $excel_file = $request->file('excel_file');

        $rows = $excel->toCollection([], $excel_file)->first();
        $is_first_row = true;

        try {

            DB::beginTransaction();

            foreach ($rows as $key => $row) {

                if ($is_first_row) {
                    if (strtolower($row[1]) != 'sku name') {
                        DB::rollBack();
                        Session::flash('alert-danger', 'Second column needs to be "SKU Name"');

                        return redirect()->back();
                    }

                    if (strtolower($row[2]) != 'product sku') {
                        DB::rollBack();
                        Session::flash('alert-danger', 'Third column needs to be "SKU Code"');

                        return redirect()->back();
                    }

                    if (strtolower($row[4]) != 'unit of measure') {
                        DB::rollBack();
                        Session::flash('alert-danger', 'Fourth column needs to be "Unit of Measure"');

                        return redirect()->back();
                    }

                    if (strtolower($row[5]) != 'customer') {
                        DB::rollBack();
                        Session::flash('alert-danger', '5th column needs to be "Customer"');

                        return redirect()->back();
                    }

                    if (strtolower($row[6]) != 'cubage (m3)') {
                        DB::rollBack();
                        Session::flash('alert-danger', '6th column needs to be "Cubage"');

                        return redirect()->back();
                    }

                    if (strtolower($row[7]) != 'weight (kg)') {
                        DB::rollBack();
                        Session::flash('alert-danger', '7th column needs to be "Weight"');

                        return redirect()->back();
                    }

                    if (strtolower($row[8]) != 'active & inactive') {
                        DB::rollBack();
                        Session::flash('alert-danger', '8th column needs to be "Active & Inactive"');

                        return redirect()->back();
                    }

                    $is_first_row = false;

                    continue; // Skip processing for the header row
                }

                if (empty(trim($row[0])) && empty(trim($row[1])) && empty(trim($row[2]))) {
                    continue;
                }

                $warehouse_customer = WarehouseCustomer::where('name', 'like', '%'.$row[5].'%')->where('warehouse_id', $warehouse->id)->first();
                $uom = WarehouseUomType::where('name', 'like', '%'.$row[5].'%')->where('warehouse_id', $warehouse->id)->first();

                $status = 1;

                if (strtolower($row[8]) == 'active' || $row[8] == 1) {
                    $status = 1;

                } elseif (strtolower($row[8]) == 'inactive' || $row[8] == 0) {
                    $status = 0;
                }

                $rack = new WarehouseCustomerProduct;
                $rack->name = $row[1] ?? 'NaN';
                $rack->sku = $row[2] ?? 'NaN'.$key;
                $rack->description = null;
                $rack->cubage = $row[6] ?? null;
                $rack->weight = $row[7] ?? null;
                $rack->is_active = $status;
                $rack->warehouse_uom_type_id = $uom ? $uom->id : null;
                $rack->warehouse_customer_id = $warehouse_customer ? $warehouse_customer->id : null;
                $rack->save();
            }

            DB::commit();

            Session::flash('alert-success', 'Excel data successfully imported');

            return redirect()->back();

        } catch (\Exception $e) {

            DB::rollBack();
            Log::info($e->getMessage());
            Session::flash('alert-danger', 'Error found in the excel file. Kindly check your file and import again');

            return redirect()->back()->with('error', 'Error found in the excel file. Kindly check your file and import again');
        }
    }

    public function updateSequence(Request $request, Warehouse $warehouse)
    {
        $validator = Validator::make($request->all(), [
            'sequences' => 'required|array',
            'sequences.*.id' => 'required|exists:warehouse_customer_products,id',
            'sequences.*.sequence' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => 'Invalid data provided.'], 400);
        }

        try {
            // Simple approach: just update the moved items with their new sequences
            // The frontend calculates the correct global sequence position
            foreach ($request->sequences as $item) {
                WarehouseCustomerProduct::where('id', $item['id'])
                    ->update(['sequence' => $item['sequence']]);
            }

            return response()->json(['success' => true, 'message' => 'Sequence updated successfully.']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to update sequence.'], 500);
        }
    }

    public function export(Warehouse $warehouse)
    {
        $query = $warehouse->customerProducts()
            ->with(['uomType', 'warehouseCustomer', 'productStocks'])
            ->orderByRaw('sequence IS NULL, sequence ASC')
            ->get();

        return ExcelFacade::download(new AdminProductExport($query), 'products_' . $warehouse->name . '_' . now()->format('Y-m-d_H-i-s') . '.xlsx');
    }
}
