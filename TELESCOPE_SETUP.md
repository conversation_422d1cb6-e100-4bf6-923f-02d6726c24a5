# Laravel Telescope Configuration

This document explains how Laravel Telescope is configured in this project for different environments.

## Overview

Laravel Telescope has been configured to:
- **Local Environment**: Show all data and allow access without authentication
- **Staging & Production**: Only show errors and require user authentication

## Authentication

### Local Environment
- No authentication required
- Full access to all Telescope features

### Staging & Production Environments
- Requires authentication from any of these guards:
  - `admin` - Admin users
  - `staff` - Staff users  
  - `customer` - Customer users
  - `web` - Regular web users

## Data Filtering

### Local Environment
- All Telescope entries are recorded and displayed
- All watchers are enabled by default

### Staging & Production Environments
- Only the following types of entries are recorded:
  - Exceptions and reportable errors
  - Failed jobs
  - Error-level logs (error, critical, alert, emergency)
  - HTTP requests with error status codes (4xx, 5xx)

## Watchers Configuration

### Always Enabled (All Environments)
- **ExceptionWatcher**: Tracks exceptions and errors
- **JobWatcher**: Tracks failed jobs
- **LogWatcher**: Tracks logs (error level only in staging/production)
- **RequestWatcher**: Tracks HTTP requests (error responses only in staging/production)

### Local Environment Only
- BatchWatcher
- CacheWatcher
- ClientRequestWatcher
- CommandWatcher
- DumpWatcher
- EventWatcher
- GateWatcher
- MailWatcher
- ModelWatcher
- NotificationWatcher
- QueryWatcher
- RedisWatcher
- ScheduleWatcher
- ViewWatcher

## Environment Variables

### Required for All Environments
```env
TELESCOPE_ENABLED=true
TELESCOPE_PATH=telescope
TELESCOPE_DRIVER=database
```

### Staging Environment Example
```env
APP_ENV=staging
TELESCOPE_ENABLED=true
TELESCOPE_LOG_LEVEL=error
TELESCOPE_BATCH_WATCHER=false
TELESCOPE_CACHE_WATCHER=false
TELESCOPE_CLIENT_REQUEST_WATCHER=false
TELESCOPE_COMMAND_WATCHER=false
TELESCOPE_DUMP_WATCHER=false
TELESCOPE_EVENT_WATCHER=false
TELESCOPE_GATE_WATCHER=false
TELESCOPE_MAIL_WATCHER=false
TELESCOPE_MODEL_WATCHER=false
TELESCOPE_NOTIFICATION_WATCHER=false
TELESCOPE_QUERY_WATCHER=false
TELESCOPE_REDIS_WATCHER=false
TELESCOPE_SCHEDULE_WATCHER=false
TELESCOPE_VIEW_WATCHER=false
```

### Production Environment Example
```env
APP_ENV=production
TELESCOPE_ENABLED=true
TELESCOPE_LOG_LEVEL=error
# All optional watchers are automatically disabled in production
```

## Accessing Telescope

1. **Local**: Visit `http://your-app.local/telescope`
2. **Staging/Production**: 
   - First log in to your application using any of the available portals:
     - Admin: `/admin/login`
     - Staff: `/staff/login` 
     - Customer: `/customer/login`
   - Then visit `/telescope`

## Performance Considerations

- In staging and production, most watchers are disabled to reduce performance impact
- Only error-related data is stored, significantly reducing database storage requirements
- Request watcher only captures failed requests (4xx, 5xx status codes)

## Security Notes

- Sensitive request details (tokens, headers) are automatically hidden in non-local environments
- Only authenticated users can access Telescope in staging and production
- No specific email whitelist required - any authenticated user can access
