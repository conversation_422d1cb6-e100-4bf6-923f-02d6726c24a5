<?php

use App\Http\Controllers\Customer\CalendarController;
use App\Http\Controllers\Customer\DashboardController;
use App\Http\Controllers\Customer\DocumentController;
use App\Http\Controllers\Customer\ImageController;
use App\Http\Controllers\Customer\LoginController;
use App\Http\Controllers\Customer\MeController;
use App\Http\Controllers\Customer\NotificationController;
use App\Http\Controllers\Customer\OrderStockInController;
use App\Http\Controllers\Customer\OrderStockOutController;
use App\Http\Controllers\Customer\ProductController;
use App\Http\Controllers\Customer\WarehouseController;
use Illuminate\Support\Facades\Route;

Route::controller(LoginController::class)->group(function () {
    Route::get('login', 'index')->name('.login')->middleware('guest:customer');
    Route::post('login', 'authenticate')->name('.login')->middleware('guest:customer');
    Route::any('logout', 'logout')->name('.logout')->middleware('auth:customer');
});

Route::middleware(['auth:customer'])->scopeBindings()->group(function () {
    Route::get('', [DashboardController::class, 'switch'])->name('.switch');

    Route::group(['prefix' => 'me', 'as' => '.me'], function () {
        Route::get('edit', [MeController::class, 'edit'])->name('.edit');
        Route::post('update', [MeController::class, 'update'])->name('.update');
        Route::get('edit-password', [MeController::class, 'editPassword'])->name('.edit-password');
        Route::post('update-password', [MeController::class, 'updatePassword'])->name('.update-password');
    });

    Route::prefix('notifications')->name('.notification')->controller(NotificationController::class)->group(function () {
        Route::get('index', 'index');
        Route::post('query', 'query')->name('.query');
        Route::post('mark-read', [NotificationController::class, 'markReadNRedirect'])->name('.notification-read');
        Route::post('all-mark-read', [NotificationController::class, 'markAllRead'])->name('.notification-all-read');
    });

    Route::prefix('{warehouse:slug}')->group(function () {
        Route::get('show', [WarehouseController::class, 'show'])->name('.show');

        Route::prefix('calendar')->name('.calendar')->controller(CalendarController::class)->group(function () {
            Route::get('', 'index');
            Route::get('event', 'event')->name('.event');
        });

        Route::prefix('images')->name('.image')->controller(ImageController::class)->group(function () {
            Route::prefix('dropzone')->name('.dropzone')->group(function () {
                Route::get('', 'dropzone');
                Route::post('', 'upload')->name('.upload');
            });

            Route::delete('{image}', 'delete')->name('.delete');

            Route::post('ckeditor-simple-upload', 'ckeditorSimpleUpload')->name('.ckeditor-simple-upload');
        });

        Route::prefix('products')->name('.product')->controller(ProductController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::get('export', 'export')->name('.export');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('{customer_product}/edit', 'edit')->name('.edit');
            Route::post('{customer_product}', 'update')->name('.update');
            Route::delete('{customer_product}', 'delete')->name('.delete');
        });

        Route::prefix('stock-in')->name('.stock-in')->controller(OrderStockInController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('/get-warehouse-customer-products', 'getWarehouseCustomerProducts')->name('.get-warehouse-customer-products');
        });

        Route::prefix('stock-out')->name('.stock-out')->controller(OrderStockOutController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('/get-warehouse-customer-products', 'getWarehouseCustomerProducts')->name('.get-warehouse-customer-products');
        });

        Route::prefix('documents')->name('.document')->controller(DocumentController::class)->group(function () {
            Route::get('', 'index')->name('');
            Route::post('query', 'query')->name('.query');
            Route::get('cargo-report', 'cargoReport')->name('.cargo-report');
            Route::post('generate-cargo-report', 'generateCargoReport')->name('.generate-cargo-report');
            Route::get('excel-report/{document_id}', 'excelReport')->name('.download-excel-report');
        });
    });
});
