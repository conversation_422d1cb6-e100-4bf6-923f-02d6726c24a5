<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\CalendarController;
use App\Http\Controllers\Admin\CompanyController;
use App\Http\Controllers\Admin\CompanyWarehouseController;
use App\Http\Controllers\Admin\CustomerController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\DocumentController;
use App\Http\Controllers\Admin\ImageController;
use App\Http\Controllers\Admin\LoginController;
use App\Http\Controllers\Admin\MeController;
use App\Http\Controllers\Admin\NotificationController;
use App\Http\Controllers\Admin\OrderStockInController;
use App\Http\Controllers\Admin\OrderStockInPutAwayController;
use App\Http\Controllers\Admin\OrderStockInQcController;
use App\Http\Controllers\Admin\OrderStockOutCollectController;
use App\Http\Controllers\Admin\OrderStockOutController;
use App\Http\Controllers\Admin\OrderStockOutPickingController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\Admin\ProductOrderController;
use App\Http\Controllers\Admin\StaffController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\UserProductController;
use App\Http\Controllers\Admin\WarehouseController;
use App\Http\Controllers\Admin\WarehouseCustomerController;
use App\Http\Controllers\Admin\WarehouseProductCategoryController;
use App\Http\Controllers\Admin\WarehouseProductController;
use App\Http\Controllers\Admin\WarehouseRackController;
use App\Http\Controllers\Admin\WarehouseRackProductController;
use App\Http\Controllers\Admin\WarehouseRoleController;
use App\Http\Controllers\Admin\WarehouseTeamController;
use App\Http\Controllers\Admin\WarehouseUomTypeController;
use App\Http\Controllers\Admin\WarehouseUserController;
use App\Http\Controllers\Admin\WarehouseZoneController;

Route::controller(LoginController::class)->group(function () {
    Route::get('login', 'index')->name('.login')->middleware('guest:admin');
    Route::post('login', 'authenticate')->name('.login')->middleware('guest:admin');
    Route::any('logout', 'logout')->name('.logout')->middleware('auth:admin');
});

Route::middleware(['auth:admin'])->scopeBindings()->group(function () {
    Route::get('', DashboardController::class);

    Route::prefix('calendar')->name('.calendar')->controller(CalendarController::class)->group(function () {
        Route::get('', 'index');
        Route::get('event', 'event')->name('.event');
    });

    Route::prefix('images')->name('.image')->controller(ImageController::class)->group(function () {
        Route::prefix('dropzone')->name('.dropzone')->group(function () {
            Route::get('', 'dropzone');
            Route::post('', 'upload')->name('.upload');
        });

        Route::delete('{image}', 'delete')->name('.delete');

        Route::post('ckeditor-simple-upload', 'ckeditorSimpleUpload')->name('.ckeditor-simple-upload');
    });

    Route::group(['prefix' => 'me', 'as' => '.me'], function () {
        Route::get('edit', [MeController::class, 'edit'])->name('.edit');
        Route::post('update', [MeController::class, 'update'])->name('.update');
        Route::get('edit-password', [MeController::class, 'editPassword'])->name('.edit-password');
        Route::post('update-password', [MeController::class, 'updatePassword'])->name('.update-password');
    });

    Route::prefix('admins')->name('.admin')->controller(AdminController::class)->group(function () {
        Route::get('', 'index');
        Route::post('query', 'query')->name('.query');
        Route::get('create', 'create')->name('.create');
        Route::post('', 'store')->name('.store');
        Route::get('{admin}/edit', 'edit')->name('.edit');
        Route::post('{admin}', 'update')->name('.update');
        Route::delete('{admin}', 'delete')->name('.delete');
    });

    Route::prefix('users')->name('.user')->controller(UserController::class)->group(function () {
        Route::get('', 'index');
        Route::post('query', 'query')->name('.query');
        Route::get('create', 'create')->name('.create');
        Route::post('', 'store')->name('.store');
        Route::get('{user}', 'show')->name('.show');
        Route::get('{user}/edit', 'edit')->name('.edit');
        Route::post('{user}', 'update')->name('.update');
        Route::delete('{user}', 'delete')->name('.delete');
        Route::get('{user}/reset-password', 'resetPassword')->name('.reset-password');

        Route::prefix('{user}/products')->name('.product')->controller(UserProductController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
        });
    });

    Route::prefix('companies')->name('.company')->controller(CompanyController::class)->group(function () {
        Route::get('', 'index');
        Route::post('query', 'query')->name('.query');
        Route::get('create', 'create')->name('.create');
        Route::post('', 'store')->name('.store');
        Route::get('{company}', 'show')->name('.show');
        Route::get('{company}/edit', 'edit')->name('.edit');
        Route::post('{company}', 'update')->name('.update');
        Route::delete('{company}', 'delete')->name('.delete');

        Route::prefix('{company}/warehouses')->name('.warehouse')->controller(CompanyWarehouseController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
        });
    });

    Route::post('bulk-import-excel/{warehouse}', [WarehouseRackController::class, 'readNImportExcelRow'])->name('.import-excel');
    Route::post('bulk-import-sku-excel/{warehouse}', [WarehouseProductController::class, 'readNImportExcelRow'])->name('.import-sku-excel');

    Route::prefix('notifications')->name('.notification')->controller(NotificationController::class)->group(function () {
        Route::get('index', 'index');
        Route::post('query', 'query')->name('.query');
        Route::post('mark-read', [NotificationController::class, 'markReadNRedirect'])->name('.notification-read');
        Route::post('all-mark-read', [NotificationController::class, 'markAllRead'])->name('.notification-all-read');
    });

    Route::prefix('warehouses')->name('.warehouse')->controller(WarehouseController::class)->group(function () {
        Route::get('', 'index');
        Route::post('query', 'query')->name('.query');
        Route::get('create', 'create')->name('.create');
        Route::post('', 'store')->name('.store');
        Route::get('{warehouse}', 'show')->name('.show');
        Route::get('{warehouse}/edit', 'edit')->name('.edit');
        Route::post('{warehouse}', 'update')->name('.update');
        Route::delete('{warehouse}', 'delete')->name('.delete');
        Route::get('{warehouseId}/get-customers', 'getCustomers')->name('.get-customers');

        Route::prefix('{warehouse}/zones')->name('.zone')->controller(WarehouseZoneController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('{zone}/edit', 'edit')->name('.edit');
            Route::post('{zone}', 'update')->name('.update');
            Route::delete('{zone}', 'delete')->name('.delete');
        });

        Route::prefix('{warehouse}/racks')->name('.rack')->controller(WarehouseRackController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('{rack}/edit', 'edit')->name('.edit');
            Route::post('{rack}', 'update')->name('.update');
            Route::delete('{rack}', 'delete')->name('.delete');
            Route::get('{rack}/qr-code', 'showQrCode')->name('.show-qr');

            Route::prefix('{rack}/products')->name('.product')->controller(WarehouseRackProductController::class)->group(function () {
                Route::get('', 'index');
                Route::post('query', 'query')->name('.query');
            });
        });

        Route::prefix('{warehouse}/teams')->name('.team')->controller(WarehouseTeamController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('{team}/edit', 'edit')->name('.edit');
            Route::post('{team}', 'update')->name('.update');
            Route::delete('{team}', 'delete')->name('.delete');
        });

        Route::prefix('{warehouse}/roles')->name('.role')->controller(WarehouseRoleController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('{role}/edit', 'edit')->name('.edit');
            Route::post('{role}', 'update')->name('.update');
            Route::delete('{role}', 'delete')->name('.delete');
        });

        Route::prefix('{warehouse}/users')->name('.user')->controller(WarehouseUserController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('{warehouse_user}/edit', 'edit')->name('.edit');
            Route::post('{warehouse_user}', 'update')->name('.update');
            Route::delete('{warehouse_user}', 'delete')->name('.delete');
        });

        Route::prefix('{warehouse}/customers')->name('.customer')->controller(WarehouseCustomerController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('{warehouse_customer}/edit', 'edit')->name('.edit');
            Route::post('{warehouse_customer}', 'update')->name('.update');
            Route::delete('{warehouse_customer}', 'delete')->name('.delete');
            Route::post('{warehouse_customer}/reset-password', 'resetPwd')->name('.reset-pwd');
        });

        Route::prefix('{warehouse}/categories')->name('.category')->controller(WarehouseProductCategoryController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('{product_category}/edit', 'edit')->name('.edit');
            Route::post('{product_category}', 'update')->name('.update');
            Route::delete('{product_category}', 'delete')->name('.delete');
        });

        Route::prefix('{warehouse}/uom-types')->name('.uom')->controller(WarehouseUomTypeController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('{uom_type}/edit', 'edit')->name('.edit');
            Route::post('{uom_type}', 'update')->name('.update');
            Route::delete('{uom_type}', 'delete')->name('.delete');
        });

        Route::prefix('{warehouse}/products')->name('.product')->controller(WarehouseProductController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
            Route::post('update-sequence', 'updateSequence')->name('.update-sequence');
            Route::get('export', 'export')->name('.export');
            Route::get('create', 'create')->name('.create');
            Route::post('', 'store')->name('.store');
            Route::get('{customer_product}/edit', 'edit')->name('.edit');
            Route::post('{customer_product}', 'update')->name('.update');
            Route::delete('{customer_product}', 'delete')->name('.delete');
        });
    });

    Route::prefix('staffs')->name('.staff')->controller(StaffController::class)->group(function () {
        Route::get('', 'index');
        Route::post('query', 'query')->name('.query');
    });

    Route::prefix('customers')->name('.customer')->controller(CustomerController::class)->group(function () {
        Route::get('', 'index');
        Route::post('query', 'query')->name('.query');
    });

    Route::prefix('products')->name('.product')->controller(ProductController::class)->group(function () {
        Route::get('', 'index');
        Route::post('query', 'query')->name('.query');

        Route::prefix('{product}/orders')->name('.order')->controller(ProductOrderController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
        });
    });

    Route::prefix('documents')->name('.document')->controller(DocumentController::class)->group(function () {
        Route::get('', 'index');
        Route::post('query', 'query')->name('.query');
        Route::get('create', 'create')->name('.create');
        Route::post('', 'store')->name('.store');
        Route::get('/get-warehouse-racks/{warehouse_id}', 'getWarehouseRacks')->name('.get-warehouse-racks');
        Route::get('/get-warehouse-products/{warehouse_id}', 'getWarehouseProducts')->name('.get-warehouse-products');
        Route::get('/get-warehouse-rack-products/{warehouse_rack_id}', 'getWarehouseRackProducts')->name('.get-warehouse-rack-products');
        Route::get('/download/excel-report/{document_id}', 'excelReport')->name('.download-excel-report');
        Route::get('/cargo-report', 'cargoReport')->name('.cargo-report');
        Route::post('/generate-report', 'generateCargoReport')->name('.generate-cargo-report');
    });

    Route::prefix('stock-in')->name('.stock-in')->controller(OrderStockInController::class)->group(function () {
        Route::get('', 'index');
        Route::post('query', 'query')->name('.query');
        Route::get('create', 'create')->name('.create');
        Route::post('', 'store')->name('.store');
        Route::get('{order}', 'show')->name('.show');
        Route::get('{order}/edit', 'edit')->name('.edit');
        Route::post('{order}/update', 'update')->name('.update');
        Route::get('/get-warehouse-customers/{warehouse_id}', 'getWarehouseCustomers')->name('.get-warehouse-customers');
        Route::get('/get-warehouse-customer-products/{warehouse_customer_id}', 'getWarehouseCustomerProducts')->name('.get-warehouse-customer-products');
        Route::post('{order}/update-status', 'updateStatus')->name('.update-status');
        Route::get('{order}/qr-download/{type}', 'downloadQrCode')->name('.qr-download');
        Route::get('{order}/qr-image/{size}', 'displayQrImage')->name('.qr-image');

        Route::prefix('{order}/qc')->name('.qc')->controller(OrderStockInQcController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
        });

        Route::prefix('{order}/put-away')->name('.put-away')->controller(OrderStockInPutAwayController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
        });
    });

    Route::prefix('stock-out')->name('.stock-out')->controller(OrderStockOutController::class)->group(function () {
        Route::get('', 'index');
        Route::post('query', 'query')->name('.query');
        Route::get('create', 'create')->name('.create');
        Route::post('', 'store')->name('.store');
        Route::get('{order}', 'show')->name('.show');
        Route::get('{order}/edit', 'edit')->name('.edit');
        Route::post('{order}/update', 'update')->name('.update');
        Route::get('/get-warehouse-customers/{warehouse_id}', 'getWarehouseCustomers')->name('.get-warehouse-customers');
        Route::get('/get-warehouse-customer-products/{warehouse_customer_id}', 'getWarehouseCustomerProducts')->name('.get-warehouse-customer-products');
        Route::post('{order}/update-status', 'updateStatus')->name('.update-status');
        Route::post('{order}/generate-delivery-order', 'generateDeliveryOrder')->name('.generate-delivery-order');
        Route::get('{order}/qr-download/{type}', 'downloadQrCode')->name('.qr-download');
        Route::get('{order}/qr-image/{size}', 'displayQrImage')->name('.qr-image');

        Route::prefix('{order}/picking')->name('.picking')->controller(OrderStockOutPickingController::class)->group(function () {
            Route::get('', 'index');
            Route::post('query', 'query')->name('.query');
        });

        Route::prefix('{order}/collect')->name('.collect')->controller(OrderStockOutCollectController::class)->group(function () {
            Route::get('', 'index');
        });
    });
});
