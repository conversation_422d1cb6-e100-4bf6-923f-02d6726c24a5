@extends('admin.layouts.index')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_toolbar" class="app-toolbar pt-10 mb-0">
            <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex align-items-stretch">
                <div class="app-toolbar-wrapper d-flex flex-stack flex-wrap gap-4 w-100">
                    <div class="page-title d-flex flex-column justify-content-center gap-1 me-3">
                        <h1 class="page-heading d-flex flex-column justify-content-center text-gray-900 fw-bold fs-3 m-0">SKUs</h1>
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin') }}" class="text-hover-primary">Home</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.warehouse') }}" class="text-hover-primary">Warehouses</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item text-muted">
                                <a href="{{ route('admin.warehouse.show', $warehouse) }}">{{ $warehouse->name }}</a>
                            </li>
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-500 w-5px h-2px"></span>
                            </li>
                            <li class="breadcrumb-item text-muted">
                                SKUs
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                @include('admin.warehouse._menu')
                <div class="card mb-5 mb-xl-10" id="kt_profile_details_view">
                    <div class="card-header cursor-pointer">
                        <div class="card-title m-0">
                            <h3 class="fw-bold m-0">SKUs</h3>
                        </div>
                        <div class="d-flex align-items-center gap-2 gap-lg-3">
                            <a href="{{ route('admin.warehouse.product.export', $warehouse) }}" class="btn btn-sm btn-flex btn-success" id="exportBtn">
                                <i class="ki-outline ki-file-down fs-4"></i>Export Excel
                            </a>
                            <button class="btn btn-sm btn-flex btn-secondary" data-bs-toggle="modal" data-bs-target="#importExcelModal">
                                <i class="ki-outline ki-file-up fs-4"></i>Import Excel
                            </button>
                            <a href="{{ route('admin.warehouse.product.create', $warehouse) }}" class="btn btn-sm btn-primary">
                                <i class="ki-outline ki-plus fs-4"></i>Create
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="text-muted">
                                <small><i class="ki-duotone ki-information-5 fs-6 text-info me-1">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                </i>Tip: Select "All" entries to reorder across all products</small>
                            </div>
                        </div>
                        <div class="table-responsive" style="min-width: 1200px; overflow-x: auto;">
                            <table id="dataTable" class="table table-row-dashed dataTable no-footer" style="width: 100%;">
                                <thead>
                                    <tr class="fw-semibold fs-6 text-muted">
                                        <th style="white-space: nowrap;">
                                            <i class="ki-duotone ki-arrows-circle fs-4 text-primary me-2">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>Sequence
                                        </th>
                                        <th style="white-space: nowrap;">Image</th>
                                        <th style="white-space: nowrap;">SKU Code</th>
                                        <th style="white-space: nowrap;">SKU Name</th>
                                        <th style="white-space: nowrap;">Warehouse Customer</th>
                                        <th style="white-space: nowrap;">UOM</th>
                                        <th style="white-space: nowrap;">Total Cubage (m³)</th>
                                        <th style="white-space: nowrap;">Total Weight (kg)</th>
                                        <th style="white-space: nowrap;">Total Available Stock</th>
                                        <th style="white-space: nowrap;">Status</th>
                                        <th style="white-space: nowrap;">Created at</th>
                                        <th style="white-space: nowrap;">Actions</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- File import Modal -->
    <div class="modal fade" id="importExcelModal" tabindex="-1" aria-labelledby="importExcelLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                {{-- {!! html()->form('POST', route('admin.import-excel', ['warehouse' => $warehouse->id]),['enctype' => 'multipart/form-data'] )->class('form d-inline')->acceptsFiles()->open() !!} --}}
                <form action="{{ route('admin.import-sku-excel', ['warehouse' => $warehouse->id]) }}" method="POST" enctype="multipart/form-data" class="form d-inline">
                    @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="importExcelLabel">Import SKUs</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Load From Excel
                    <div class="input-group has-validation">
                        {!! html()->file('excel_file')->class('form-control mt-3' . $errors->first('reason', ' is-invalid'))->attribute('required', 'required')->accept('.xlsx, .xls') !!}
                        @if ($errors->any())
                        <div class="text-danger">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </div>
                        @endif
                    </div>
                </div>
                <div class="modal-footer mt-5">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <!-- Use a button inside the modal to trigger the form submission -->
                    <button type="submit" class="btn btn-danger">Import</button>
                </div>
                </form>
                {{-- {!! html()->form()->close() !!} --}}
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        });

        var oTable = $('#dataTable').DataTable({
            ajax: {
                url: "{{ route('admin.warehouse.product.query', $warehouse) }}",
                method: "POST"
            },
            serverSide: true,
            order: [
                [10, 'desc']
            ],
            bFilter: true,
            scrollX: true,
            scrollCollapse: true,
            autoWidth: false,
            responsive: {
                details: {
                    type: 'column',
                    target: 'tr'
                }
            },
            columnDefs: [
                {
                    targets: 0,
                    className: 'control-disable dt-body-center'
                }
            ],
            ordering: false,
            lengthMenu: [
                [10, 25, 50, 100, -1],
                [10, 25, 50, 100, "All"]
            ],
            pageLength: 50,
            lengthChange: true,
            rowReorder: {
                selector: 'td:nth-child(1)', // Sequence column (first)
                update: false
            },
            columns: [{
                    data: "sequence",
                    orderable: false,
                    searchable: false,
                    width: "100px",
                    className: 'reorder text-center fw-bold',
                    render: function(data, type, row) {
                        if (data) {
                            return '<span class="badge badge-primary sequence-badge fs-6 px-3 py-2">' + data + '</span>';
                        } else {
                            return '<span class="badge badge-light sequence-badge fs-6 px-3 py-2 text-muted">-</span>';
                        }
                    }
                },
                {
                    data: "image",
                    orderable: false,
                    searchable: false,
                    width: "70px",
                    className: 'dt-body-center'
                },
                {
                    data: "sku",
                    orderable: false,
                    width: "150px",
                },
                {
                    data: "name",
                    orderable: false,
                    width: "200px"
                },
                {
                    data: "customer",
                    orderable: false,
                    width: "150px",
                },
                {
                    data: "uomType.name",
                    orderable: false,
                    width: "80px",
                },
                {
                    data: "total_cubage",
                    orderable: false,
                    width: "120px",
                },
                {
                    data: "total_weight",
                    orderable: false,
                    width: "120px",
                },
                {
                    data: "total_stock",
                    orderable: false,
                    width: "120px",
                },
                {
                    data: "is_active",
                    orderable: false,
                    searchable: false,
                    width: "80px",
                },
                {
                    data: "created_at",
                    searchable: false,
                    width: "120px",
                },
                {
                    data: "actions",
                    orderable: false,
                    searchable: false,
                    width: "100px",
                }
            ]
        });

        // Handle row reordering
        oTable.on('row-reorder', function (e, diff, edit) {
            if (diff.length === 0) {
                return;
            }

            // Show loading indicator
            Swal.fire({
                title: 'Updating Sequence...',
                text: 'Please wait while we update the product sequence.',
                icon: 'info',
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            let sequences = [];
            let pageInfo = oTable.page.info();
            let pageStart = pageInfo.start;

            for (let i = 0; i < diff.length; i++) {
                let rowData = oTable.row(diff[i].node).data();
                // Calculate global sequence position
                let globalSequence = pageStart + diff[i].newPosition + 1;
                sequences.push({
                    id: rowData.id,
                    sequence: globalSequence
                });
            }

            // Update sequences via AJAX
            $.ajax({
                url: "{{ route('admin.warehouse.product.update-sequence', $warehouse) }}",
                method: 'POST',
                data: {
                    sequences: sequences,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            title: 'Success!',
                            text: 'Product sequence has been updated successfully.',
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false,
                            toast: false,
                            position: 'center'
                        });
                        oTable.ajax.reload();
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Failed to update sequence. Please try again.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Failed to update sequence. Please check your connection and try again.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        });
    </script>

    <style>
        /* Sequence column styling */
        .reorder {
            cursor: grab !important;
            transition: all 0.2s ease;
        }

        .reorder:hover {
            background-color: #f8f9fa !important;
            cursor: grabbing !important;
        }

        .reorder .badge {
            cursor: grab !important;
            transition: all 0.3s ease;
        }

        .reorder:hover .badge {
            transform: scale(1.05);
            cursor: grabbing !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        /* Row reorder styling */
        .dt-rowReorder-moving {
            background-color: #fff3cd !important;
            border: 2px dashed #ffc107 !important;
            opacity: 0.8;
        }

        .dt-rowReorder-drop-marker {
            background-color: #28a745 !important;
            height: 3px !important;
            box-shadow: 0 0 5px rgba(40, 167, 69, 0.5);
        }

        /* Subtle sequence column distinction */
        td:first-child {
            border-right: 1px solid #e9ecef;
        }

        /* Badge styling enhancements */
        .sequence-badge {
            font-weight: 600;
            letter-spacing: 0.5px;
        }
    </style>
@endpush
